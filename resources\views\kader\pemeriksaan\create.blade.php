<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Input Pemeriksaan untuk Santri: ' . $santri->nama) }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <form method="POST" action="{{ route('pemeriksaan.store', $santri->id) }}">
                        @csrf

                        <!-- Antropometri -->
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Antropometri</h3>
                        <div class="mb-4">
                            <label for="berat_badan" class="block text-sm font-medium text-gray-700">Berat <PERSON> (kg)</label>
                            <input type="number" step="0.01" name="berat_badan" id="berat_badan" class="form-input mt-1 block w-full">
                        </div>
                        <div class="mb-4">
                            <label for="tinggi_badan" class="block text-sm font-medium text-gray-700">Tinggi Badan (cm)</label>
                            <input type="number" step="0.01" name="tinggi_badan" id="tinggi_badan" class="form-input mt-1 block w-full">
                        </div>
                        <div class="mb-4">
                            <label for="lila" class="block text-sm font-medium text-gray-700">LILA (cm)</label>
                            <input type="number" step="0.01" name="lila" id="lila" class="form-input mt-1 block w-full">
                        </div>
                        <div class="mb-4">
                            <label for="lingkar_perut" class="block text-sm font-medium text-gray-700">Lingkar Perut (cm)</label>
                            <input type="number" step="0.01" name="lingkar_perut" id="lingkar_perut" class="form-input mt-1 block w-full">
                        </div>
                        <div class="mb-4">
                            <label for="imt" class="block text-sm font-medium text-gray-700">IMT</label>
                            <input type="number" step="0.01" name="imt" id="imt" class="form-input mt-1 block w-full" readonly>
                        </div>

                        <!-- Tekanan Darah -->
                        <h3 class="text-lg font-medium text-gray-900 mb-4 mt-6">Tekanan Darah</h3>
                        <div class="mb-4">
                            <label for="tekanan_darah" class="block text-sm font-medium text-gray-700">Tekanan Darah (mmHg)</label>
                            <input type="text" name="tekanan_darah" id="tekanan_darah" class="form-input mt-1 block w-full">
                        </div>

                        <!-- Hemoglobin (Hb) -->
                        <h3 class="text-lg font-medium text-gray-900 mb-4 mt-6">Hemoglobin (Hb)</h3>
                        <div class="mb-4">
                            <label for="hemoglobin" class="block text-sm font-medium text-gray-700">Hemoglobin (g/dL)</label>
                            <input type="number" step="0.01" name="hemoglobin" id="hemoglobin" class="form-input mt-1 block w-full">
                        </div>

                        <!-- Masalah / Konseling -->
                        <h3 class="text-lg font-medium text-gray-900 mb-4 mt-6">Masalah / Konseling</h3>
                        <div class="mb-4">
                            <label for="masalah" class="block text-sm font-medium text-gray-700">Masalah / Konseling</label>
                            <textarea name="masalah" id="masalah" class="form-textarea mt-1 block w-full"></textarea>
                        </div>

                        <!-- Tablet Tambah Darah -->
                        <h3 class="text-lg font-medium text-gray-900 mb-4 mt-6">Tablet Tambah Darah</h3>
                        <div class="mb-4">
                            <label class="inline-flex items-center">
                                <input type="checkbox" name="tablet_tambah_darah" value="1" class="form-checkbox">
                                <span class="ml-2 text-sm text-gray-600">Diberikan</span>
                            </label>
                        </div>

                        <div class="flex items-center justify-end mt-4">
                            <button type="submit" class="btn btn-primary">
                                Simpan Pemeriksaan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const beratBadanInput = document.getElementById('berat_badan');
            const tinggiBadanInput = document.getElementById('tinggi_badan');
            const imtInput = document.getElementById('imt');

            function calculateIMT() {
                const beratBadan = parseFloat(beratBadanInput.value);
                const tinggiBadanCm = parseFloat(tinggiBadanInput.value);

                if (!isNaN(beratBadan) && !isNaN(tinggiBadanCm) && tinggiBadanCm > 0) {
                    const tinggiBadanM = tinggiBadanCm / 100;
                    const imt = beratBadan / (tinggiBadanM * tinggiBadanM);
                    imtInput.value = imt.toFixed(2);
                } else {
                    imtInput.value = '';
                }
            }

            beratBadanInput.addEventListener('input', calculateIMT);
            tinggiBadanInput.addEventListener('input', calculateIMT);
        });
    </script>
</x-app-layout>