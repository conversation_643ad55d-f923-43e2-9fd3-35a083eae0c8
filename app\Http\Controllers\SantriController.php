<?php

namespace App\Http\Controllers;

use App\Models\Santri;
use Illuminate\Http\Request;

class SantriController extends Controller
{
    public function index()
    {
        $santris = Santri::with('pemeriksaans')->get();
        return view('santri.index', compact('santris'));
    }

    public function create()
    {
        return view('santri.create');
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'nama' => 'required|string|max:255',
            'umur' => 'required|integer',
            'jenis_kelamin' => 'required|string',
            'kelas' => 'required|string|max:255',
            'nis' => 'required|string|unique:santris,nis',
            'epilepsi' => 'nullable|boolean',
            'asma' => 'nullable|boolean',
            'hepatitis' => 'nullable|boolean',
            'tuberculosis' => 'nullable|boolean',
            'patah_tulang' => 'nullable|boolean',
            'hernia' => 'nullable|boolean',
            'penyakit_lainnya' => 'nullable|string',
            'jantung' => 'nullable|boolean',
            'diabetes' => 'nullable|boolean',
            'talasemia' => 'nullable|boolean',
            'penyakit_bawaan_lainnya' => 'nullable|string',
            'alergi_obat' => 'nullable|boolean',
            'alergi_makanan' => 'nullable|boolean',
            'siklus_menstruasi' => 'nullable|string',
        ]);

        Santri::create($validatedData);

        return redirect()->route('santri.index')->with('success', 'Data santri berhasil disimpan.');
    }
}
