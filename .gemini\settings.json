{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": ""}, "alwaysAllow": ["resolve-library-id"]}, "sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "laravel-boost": {"command": "php", "args": ["artisan", "boost:mcp"], "alwaysAllow": ["application-info", "get-config", "database-schema", "list-routes", "list-artisan-commands", "search-docs", "tinker", "database-query"]}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"], "alwaysAllow": ["puppeteer_navigate"]}}}