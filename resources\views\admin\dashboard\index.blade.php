@extends('components.admin-layout')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">Dashboard Admin - Monitoring Data Santri</div>

                <div class="card-body">
                    @if (session('status'))
                        <div class="alert alert-success" role="alert">
                            {{ session('status') }}
                        </div>
                    @endif

                    <!-- Ringkasan Data -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">Total Santri</div>
                                <div class="card-body">
                                    <h5 class="card-title">{{ $adminDashboardData['total_santri'] }}</h5>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">Total Kader</div>
                                <div class="card-body">
                                    <h5 class="card-title">{{ $adminDashboardData['total_kader'] }}</h5>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">Laporan Bulanan</div>
                                <div class="card-body">
                                    <h5 class="card-title">{{ $adminDashboardData['monthly_report']['bulan'] }}</h5>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Laporan Bulanan -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">Laporan Bulanan</div>
                                <div class="card-body">
                                    <h5>{{ $adminDashboardData['monthly_report']['bulan'] }}</h5>
                                    <table class="table">
                                        <tr>
                                            <td>Jumlah Pemeriksaan</td>
                                            <td>{{ $adminDashboardData['monthly_report']['jumlah_pemeriksaan'] }}</td>
                                        </tr>
                                        <tr>
                                            <td>Anemia Ringan</td>
                                            <td>{{ $adminDashboardData['monthly_report']['anemia_ringan'] }}</td>
                                        </tr>
                                        <tr>
                                            <td>Anemia Sedang</td>
                                            <td>{{ $adminDashboardData['monthly_report']['anemia_sedang'] }}</td>
                                        </tr>
                                        <tr>
                                            <td>Anemia Berat</td>
                                            <td>{{ $adminDashboardData['monthly_report']['anemia_berat'] }}</td>
                                        </tr>
                                        <tr>
                                            <td>Gizi Kurang</td>
                                            <td>{{ $adminDashboardData['monthly_report']['gizi_kurang'] }}</td>
                                        </tr>
                                        <tr>
                                            <td>Gizi Buruk</td>
                                            <td>{{ $adminDashboardData['monthly_report']['gizi_buruk'] }}</td>
                                        </tr>
                                    </table>
                                    <button class="btn btn-primary" onclick="downloadReport()">Unduh Laporan</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Analisis Tren Kesehatan -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">Analisis Tren Kesehatan</div>
                                <div class="card-body">
                                    <canvas id="healthTrendChart" height="100"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    // Data dari controller
    const healthTrends = @json($adminDashboardData['health_trends']);

    // Grafik Tren Kesehatan
    const healthTrendCtx = document.getElementById('healthTrendChart').getContext('2d');
    const healthTrendChart = new Chart(healthTrendCtx, {
        type: 'line',
        data: {
            labels: healthTrends.labels,
            datasets: [{
                label: 'Indeks Kesehatan',
                data: healthTrends.data,
                borderColor: 'rgb(75, 192, 192)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    function downloadReport() {
        alert('Fungsi unduh laporan akan diimplementasikan di sini.');
    }
</script>
@endsection