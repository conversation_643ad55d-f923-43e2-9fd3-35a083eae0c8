<?php

namespace App\Http\Controllers;

use App\Models\Pemeriksaan;
use App\Models\Santri;
use Illuminate\Http\Request;

class PemeriksaanController extends Controller
{
    public function create($santriId)
    {
        $santri = Santri::findOrFail($santriId);
        return view('pemeriksaan.create', compact('santri'));
    }

    public function store(Request $request, $santriId)
    {
        $validatedData = $request->validate([
            'berat_badan' => 'nullable|numeric',
            'tinggi_badan' => 'nullable|numeric',
            'lila' => 'nullable|numeric',
            'lingkar_perut' => 'nullable|numeric',
            'imt' => 'nullable|numeric',
            'tekanan_darah' => 'nullable|string|max:255',
            'hemoglobin' => 'nullable|numeric',
            'masalah' => 'nullable|string',
            'tablet_tambah_darah' => 'nullable|boolean',
        ]);

        $validatedData['santri_id'] = $santriId;
        Pemeriksaan::create($validatedData);

        return redirect()->route('santri.index')->with('success', 'Data pemeriksaan berhasil disimpan.');
    }
}
