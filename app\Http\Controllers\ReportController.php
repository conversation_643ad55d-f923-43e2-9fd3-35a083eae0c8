<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Santri;
use App\Models\Pemeriksaan;

class ReportController extends Controller
{
    public function index()
    {
        // Mengambil data untuk laporan bulanan
        $monthlyReportData = $this->getMonthlyReportData();
        
        return view('kader.reports.index', compact('monthlyReportData'));
    }
    
    public function print()
    {
        $monthlyReportData = $this->getMonthlyReportData();
        return view('kader.reports.print', compact('monthlyReportData'));
    }

    private function getMonthlyReportData()
    {
        // Implementasi untuk mengambil data laporan bulanan
        // Ini adalah contoh sederhana, Anda mungkin perlu menyesuaikan dengan kebutuhan aplikasi
        $totalSantri = Santri::count();
        $totalPemeriksaan = Pemeriksaan::count();
        $anemiaRingan = Pemeriksaan::where('hemoglobin', '>=', 11)->where('hemoglobin', '<=', 12)->count();
        $anemiaSedang = Pemeriksaan::where('hemoglobin', '>=', 8)->where('hemoglobin', '<=', 10.9)->count();
        $anemiaBerat = Pemeriksaan::where('hemoglobin', '<=', 7.9)->count();
        $giziKurang = Pemeriksaan::where('imt', '<=', 18.5)->count();
        $giziBuruk = Pemeriksaan::where('imt', '<=', 17)->count();

        return [
            'bulan' => now()->format('F Y'),
            'jumlah_santri' => $totalSantri,
            'jumlah_pemeriksaan' => $totalPemeriksaan,
            'anemia_ringan' => $anemiaRingan,
            'anemia_sedang' => $anemiaSedang,
            'anemia_berat' => $anemiaBerat,
            'gizi_kurang' => $giziKurang,
            'gizi_buruk' => $giziBuruk
        ];
    }
}