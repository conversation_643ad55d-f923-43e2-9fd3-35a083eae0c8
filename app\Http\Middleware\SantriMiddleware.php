<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;
use App\Models\User;

class SantriMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        \Log::info('SantriMiddleware dijalankan');
        if (Auth::check() && Auth::user()->user_type === User::USER_TYPE_SANTRI) {
            \Log::info('SantriMiddleware: User adalah santri');
            return $next($request);
        }

        \Log::info('SantriMiddleware: User bukan santri, redirect ke /');
        return redirect('/'); // Atau halaman lain yang sesuai
    }
}
