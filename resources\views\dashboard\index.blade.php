@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">Dashboard Monitoring</div>

                <div class="card-body">
                    @if (session('status'))
                        <div class="alert alert-success" role="alert">
                            {{ session('status') }}
                        </div>
                    @endif

                    <!-- <PERSON><PERSON> -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h4><PERSON><PERSON>tum<PERSON>han</h4>
                            <canvas id="growthChart" height="100"></canvas>
                        </div>
                    </div>

                    <!-- Statistik Anemia -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h4>Statistik Anemia</h4>
                            <canvas id="anemiaChart" height="100"></canvas>
                        </div>

                        <!-- Statistik Gizi -->
                        <div class="col-md-6">
                            <h4>Statistik Gizi</h4>
                            <canvas id="nutritionChart" height="100"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    // Data dari controller
    const growthData = @json($growthData);
    const anemiaData = @json($anemiaData);
    const nutritionData = @json($nutritionData);

    // Grafik Pertumbuhan
    const growthCtx = document.getElementById('growthChart').getContext('2d');
    const growthChart = new Chart(growthCtx, {
        type: 'line',
        data: {
            labels: growthData.labels,
            datasets: [
                {
                    label: 'Berat Badan (kg)',
                    data: growthData.berat_badan,
                    borderColor: 'rgb(75, 192, 192)',
                    tension: 0.1
                },
                {
                    label: 'Tinggi Badan (cm)',
                    data: growthData.tinggi_badan,
                    borderColor: 'rgb(54, 162, 235)',
                    tension: 0.1
                },
                {
                    label: 'IMT (kg/m²)',
                    data: growthData.imt,
                    borderColor: 'rgb(255, 99, 132)',
                    tension: 0.1
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Statistik Anemia
    const anemiaCtx = document.getElementById('anemiaChart').getContext('2d');
    const anemiaChart = new Chart(anemiaCtx, {
        type: 'pie',
        data: {
            labels: anemiaData.labels,
            datasets: [{
                data: anemiaData.data,
                backgroundColor: [
                    'rgb(75, 192, 192)',
                    'rgb(255, 205, 86)',
                    'rgb(255, 159, 64)',
                    'rgb(255, 99, 132)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    // Statistik Gizi
    const nutritionCtx = document.getElementById('nutritionChart').getContext('2d');
    const nutritionChart = new Chart(nutritionCtx, {
        type: 'pie',
        data: {
            labels: nutritionData.labels,
            datasets: [{
                data: nutritionData.data,
                backgroundColor: [
                    'rgb(75, 192, 192)',
                    'rgb(255, 205, 86)',
                    'rgb(255, 99, 132)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
</script>
@endsection