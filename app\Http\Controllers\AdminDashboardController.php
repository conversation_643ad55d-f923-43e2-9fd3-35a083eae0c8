<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Santri;
use App\Models\Pemeriksaan;

class AdminDashboardController extends Controller
{
    public function index()
    {
        $totalSantri = Santri::count();
        $totalKader = \App\Models\User::where('user_type', 'kader')->count();
        $totalPemeriksaan = Pemeriksaan::count();

        // Data untuk tren kesehatan (contoh: jumlah pemeriksaan per bulan)
        $healthTrends = Pemeriksaan::selectRaw('DATE_FORMAT(created_at, "%Y-%m") as month, count(*) as total')
                                ->groupBy('month')
                                ->orderBy('month')
                                ->get();

        $months = $healthTrends->pluck('month')->toArray();
        $pemeriksaanCounts = $healthTrends->pluck('total')->toArray();

        // Statistik Anemia (contoh)
        $anemiaStats = Pemeriksaan::selectRaw('SUM(CASE WHEN hemoglobin <= 12 THEN 1 ELSE 0 END) as anemia_count, COUNT(*) as total_pemeriksaan')
                                ->first();
        $anemiaPercentage = $anemiaStats->total_pemeriksaan > 0 ? round(($anemiaStats->anemia_count / $anemiaStats->total_pemeriksaan) * 100, 2) : 0;

        // Statistik Gizi (contoh)
        $giziStats = Pemeriksaan::selectRaw('SUM(CASE WHEN imt <= 18.5 THEN 1 ELSE 0 END) as gizi_kurang_count, SUM(CASE WHEN imt > 18.5 AND imt <= 24.9 THEN 1 ELSE 0 END) as gizi_normal_count, COUNT(*) as total_pemeriksaan')
                                ->first();
        $giziKurangPercentage = $giziStats->total_pemeriksaan > 0 ? round(($giziStats->gizi_kurang_count / $giziStats->total_pemeriksaan) * 100, 2) : 0;
        $giziNormalPercentage = $giziStats->total_pemeriksaan > 0 ? round(($giziStats->gizi_normal_count / $giziStats->total_pemeriksaan) * 100, 2) : 0;
        $giziLebihPercentage = 100 - $giziKurangPercentage - $giziNormalPercentage;


        return view('admin.dashboard', compact('totalSantri', 'totalKader', 'totalPemeriksaan', 'months', 'pemeriksaanCounts', 'anemiaPercentage', 'giziKurangPercentage', 'giziNormalPercentage', 'giziLebihPercentage'));
    }
}