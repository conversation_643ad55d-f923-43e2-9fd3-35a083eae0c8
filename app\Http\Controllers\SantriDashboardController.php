<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Santri;

class SantriDashboardController extends Controller
{
    public function __invoke()
    {
        $user = Auth::user();
        $santri = Santri::where('nis', $user->nis)->with('pemeriksaans')->first();

        if (!$santri) {
            // Handle case where santri data is not found for the logged-in user
            // This might happen if a user with user_type 'santri' doesn't have a corresponding entry in the santris table
            return redirect()->route('dashboard')->with('error', 'Data santri Anda tidak ditemukan.');
        }

        return view('santri.dashboard', compact('santri'));
    }
}