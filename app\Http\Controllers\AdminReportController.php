<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Santri;
use App\Models\Pemeriksaan;
use Symfony\Component\HttpFoundation\StreamedResponse;

class AdminReportController extends Controller
{
    public function index()
    {
        return view('admin.reports.index');
    }

    public function download(Request $request)
    {
        $santris = Santri::with('pemeriksaans')->get();

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="laporan_santri_sehat.csv"',
        ];

        $callback = function() use ($santris) {
            $file = fopen('php://output', 'w');
            fputcsv($file, [
                'NIS', 'Nama', 'Umur', '<PERSON><PERSON>', 'Kelas',
                'Riwayat Penyakit Epilepsi', 'Riwayat Penyakit Asma', 'Riwayat Penyakit Hepatitis', 'Riwayat Penyakit Tuberculosis', 'Riwayat Penyakit Patah Tulang', 'Riwayat Penyakit Hernia', 'Riwayat Penyakit Lainnya',
                'Penyakit Bawaan Jantung', 'Penyakit Bawaan Diabetes', 'Penyakit Bawaan Talasemia', 'Penyakit Bawaan Lainnya',
                'Alergi Obat', 'Alergi Makanan', 'Siklus Menstruasi',
                'Tanggal Pemeriksaan', 'Berat Badan', 'Tinggi Badan', 'LILA', 'Lingkar Perut', 'IMT', 'Tekanan Darah', 'Hemoglobin', 'Masalah/Konseling', 'Tablet Tambah Darah'
            ]);

            foreach ($santris as $santri) {
                if ($santri->pemeriksaans->isEmpty()) {
                    fputcsv($file, [
                        $santri->nis, $santri->nama, $santri->umur, $santri->jenis_kelamin, $santri->kelas,
                        $santri->epilepsi ? 'Ya' : 'Tidak', $santri->asma ? 'Ya' : 'Tidak', $santri->hepatitis ? 'Ya' : 'Tidak', $santri->tuberculosis ? 'Ya' : 'Tidak', $santri->patah_tulang ? 'Ya' : 'Tidak', $santri->hernia ? 'Ya' : 'Tidak', $santri->penyakit_lainnya,
                        $santri->jantung ? 'Ya' : 'Tidak', $santri->diabetes ? 'Ya' : 'Tidak', $santri->talasemia ? 'Ya' : 'Tidak', $santri->penyakit_bawaan_lainnya,
                        $santri->alergi_obat ? 'Ya' : 'Tidak', $santri->alergi_makanan ? 'Ya' : 'Tidak', $santri->siklus_menstruasi,
                        '', '', '', '', '', '', '', '', '', '', // Kolom pemeriksaan kosong
                    ]);
                } else {
                    foreach ($santri->pemeriksaans as $pemeriksaan) {
                        fputcsv($file, [
                            $santri->nis, $santri->nama, $santri->umur, $santri->jenis_kelamin, $santri->kelas,
                            $santri->epilepsi ? 'Ya' : 'Tidak', $santri->asma ? 'Ya' : 'Tidak', $santri->hepatitis ? 'Ya' : 'Tidak', $santri->tuberculosis ? 'Ya' : 'Tidak', $santri->patah_tulang ? 'Ya' : 'Tidak', $santri->hernia ? 'Ya' : 'Tidak', $santri->penyakit_lainnya,
                            $santri->jantung ? 'Ya' : 'Tidak', $santri->diabetes ? 'Ya' : 'Tidak', $santri->talasemia ? 'Ya' : 'Tidak', $santri->penyakit_bawaan_lainnya,
                            $santri->alergi_obat ? 'Ya' : 'Tidak', $santri->alergi_makanan ? 'Ya' : 'Tidak', $santri->siklus_menstruasi,
                            $pemeriksaan->created_at->format('Y-m-d'), $pemeriksaan->berat_badan, $pemeriksaan->tinggi_badan, $pemeriksaan->lila, $pemeriksaan->lingkar_perut, $pemeriksaan->imt, $pemeriksaan->tekanan_darah, $pemeriksaan->hemoglobin, $pemeriksaan->masalah, $pemeriksaan->tablet_tambah_darah ? 'Ya' : 'Tidak'
                        ]);
                    }
                }
            }
            fclose($file);
        };

        return new StreamedResponse($callback, 200, $headers);
    }
}
