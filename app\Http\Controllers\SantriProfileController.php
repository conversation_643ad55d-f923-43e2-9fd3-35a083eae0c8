<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Santri;

class SantriProfileController extends Controller
{
    public function edit()
    {
        $user = Auth::user();
        $santri = Santri::where('nis', $user->nis)->first();

        if (!$santri) {
            // Jika data santri tidak ditemukan, buat yang baru
            $santri = new Santri();
            $santri->nis = $user->nis;
            $santri->nama = $user->name; // Asumsi nama santri sama dengan nama user
            // Set nilai default lainnya jika diperlukan
        }

        return view('santri.profile.edit', compact('santri'));
    }

    public function update(Request $request)
    {
        $user = Auth::user();
        $santri = Santri::where('nis', $user->nis)->first();

        if (!$santri) {
            $santri = new Santri();
            $santri->nis = $user->nis;
        }

        $validatedData = $request->validate([
            'nama' => 'required|string|max:255',
            'umur' => 'required|integer',
            'jenis_kelamin' => 'required|string',
            'kelas' => 'required|string|max:255',
            'epilepsi' => 'nullable|boolean',
            'asma' => 'nullable|boolean',
            'hepatitis' => 'nullable|boolean',
            'tuberculosis' => 'nullable|boolean',
            'patah_tulang' => 'nullable|boolean',
            'hernia' => 'nullable|boolean',
            'penyakit_lainnya' => 'nullable|string',
            'jantung' => 'nullable|boolean',
            'diabetes' => 'nullable|boolean',
            'talasemia' => 'nullable|boolean',
            'penyakit_bawaan_lainnya' => 'nullable|string',
            'alergi_obat' => 'nullable|boolean',
            'alergi_makanan' => 'nullable|boolean',
            'siklus_menstruasi' => 'nullable|string',
        ]);

        $santri->fill($validatedData);
        $santri->save();

        return redirect()->route('santri.profile.edit')->with('success', 'Profil santri berhasil diperbarui.');
    }
}