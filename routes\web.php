<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Route untuk Santri/Remaja
Route::middleware(['auth', 'santri'])->group(function () {
    Route::get('/santri/dashboard', App\Http\Controllers\SantriDashboardController::class)->name('santri.dashboard');
    
    // Route untuk edukasi kesehatan
    Route::get('/santri/education', [App\Http\Controllers\EducationController::class, 'index'])->name('santri.education');
    Route::get('/santri/profile', [App\Http\Controllers\SantriProfileController::class, 'edit'])->name('santri.profile.edit');
    Route::patch('/santri/profile', [App\Http\Controllers\SantriProfileController::class, 'update'])->name('santri.profile.update');
});

// Route untuk Kader Posyandu
Route::middleware(['auth', 'kader'])->group(function () {
    Route::get('/kader/dashboard', [App\Http\Controllers\DashboardController::class, 'index'])->name('kader.dashboard');
    
    // Route untuk mengelola data santri
    Route::get('/kader/santri', [App\Http\Controllers\SantriController::class, 'index'])->name('santri.index');
    Route::get('/kader/santri/create', [App\Http\Controllers\SantriController::class, 'create'])->name('santri.create');
    Route::post('/kader/santri', [App\Http\Controllers\SantriController::class, 'store'])->name('santri.store');
    
    // Route untuk mengelola data pemeriksaan
    Route::get('/kader/pemeriksaan/create/{santriId}', [App\Http\Controllers\PemeriksaanController::class, 'create'])->name('pemeriksaan.create');
    Route::post('/kader/pemeriksaan/{santriId}', [App\Http\Controllers\PemeriksaanController::class, 'store'])->name('pemeriksaan.store');
    
    // Route untuk laporan bulanan
    Route::get('/kader/reports', [App\Http\Controllers\ReportController::class, 'index'])->name('kader.reports.index');
    Route::get('/kader/reports/print', [App\Http\Controllers\ReportController::class, 'print'])->name('kader.reports.print');
});

// Route untuk Admin
Route::middleware(['auth', 'admin'])->group(function () {
    Route::get('/admin/dashboard', [App\Http\Controllers\AdminDashboardController::class, 'index'])->name('admin.dashboard');
    
    // Route untuk admin profile
    Route::get('/admin/profile', [App\Http\Controllers\AdminProfileController::class, 'edit'])->name('admin.profile.edit');
    Route::patch('/admin/profile', [App\Http\Controllers\AdminProfileController::class, 'update'])->name('admin.profile.update');
    Route::delete('/admin/profile', [App\Http\Controllers\AdminProfileController::class, 'destroy'])->name('admin.profile.destroy');
    
    // Route untuk data santri
    Route::get('/admin/santri', [App\Http\Controllers\AdminSantriController::class, 'index'])->name('admin.santri.index');
    
    // Route untuk laporan bulanan
    Route::get('/admin/reports', [App\Http\Controllers\AdminReportController::class, 'index'])->name('admin.reports.index');
    Route::get('/admin/reports/download', [App\Http\Controllers\AdminReportController::class, 'download'])->name('admin.reports.download');
    
    // Route untuk analisis tren kesehatan
    Route::get('/admin/analytics', [App\Http\Controllers\AdminAnalyticsController::class, 'index'])->name('admin.analytics.index');
});

require __DIR__.'/auth.php';
