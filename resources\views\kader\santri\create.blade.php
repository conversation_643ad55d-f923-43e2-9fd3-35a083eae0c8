<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Tambah Santri Baru') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <form method="POST" action="{{ route('santri.store') }}">
                        @csrf

                        <!-- Identitas Santri -->
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Identitas Santri</h3>
                        <div class="mb-4">
                            <label for="nama" class="block text-sm font-medium text-gray-700">Nama</label>
                            <input type="text" name="nama" id="nama" class="form-input mt-1 block w-full" required>
                        </div>
                        <div class="mb-4">
                            <label for="umur" class="block text-sm font-medium text-gray-700">Umur</label>
                            <input type="number" name="umur" id="umur" class="form-input mt-1 block w-full" required>
                        </div>
                        <div class="mb-4">
                            <label for="jenis_kelamin" class="block text-sm font-medium text-gray-700">Jenis Kelamin</label>
                            <select name="jenis_kelamin" id="jenis_kelamin" class="form-select mt-1 block w-full" required>
                                <option value="Laki-laki">Laki-laki</option>
                                <option value="Perempuan">Perempuan</option>
                            </select>
                        </div>
                        <div class="mb-4">
                            <label for="kelas" class="block text-sm font-medium text-gray-700">Kelas</label>
                            <input type="text" name="kelas" id="kelas" class="form-input mt-1 block w-full" required>
                        </div>
                        <div class="mb-4">
                            <label for="nis" class="block text-sm font-medium text-gray-700">NIS</label>
                            <input type="text" name="nis" id="nis" class="form-input mt-1 block w-full" required>
                        </div>

                        <!-- Riwayat Kesehatan - Penyakit yang pernah diderita -->
                        <h3 class="text-lg font-medium text-gray-900 mb-4 mt-6">Riwayat Kesehatan - Penyakit yang pernah diderita</h3>
                        <div class="mb-4">
                            <label class="inline-flex items-center">
                                <input type="checkbox" name="epilepsi" value="1" class="form-checkbox">
                                <span class="ml-2 text-sm text-gray-600">Epilepsi</span>
                            </label>
                        </div>
                        <div class="mb-4">
                            <label class="inline-flex items-center">
                                <input type="checkbox" name="asma" value="1" class="form-checkbox">
                                <span class="ml-2 text-sm text-gray-600">Asma</span>
                            </label>
                        </div>
                        <div class="mb-4">
                            <label class="inline-flex items-center">
                                <input type="checkbox" name="hepatitis" value="1" class="form-checkbox">
                                <span class="ml-2 text-sm text-gray-600">Hepatitis</span>
                            </label>
                        </div>
                        <div class="mb-4">
                            <label class="inline-flex items-center">
                                <input type="checkbox" name="tuberculosis" value="1" class="form-checkbox">
                                <span class="ml-2 text-sm text-gray-600">Tuberculosis</span>
                            </label>
                        </div>
                        <div class="mb-4">
                            <label class="inline-flex items-center">
                                <input type="checkbox" name="patah_tulang" value="1" class="form-checkbox">
                                <span class="ml-2 text-sm text-gray-600">Patah Tulang</span>
                            </label>
                        </div>
                        <div class="mb-4">
                            <label class="inline-flex items-center">
                                <input type="checkbox" name="hernia" value="1" class="form-checkbox">
                                <span class="ml-2 text-sm text-gray-600">Hernia</span>
                            </label>
                        </div>
                        <div class="mb-4">
                            <label for="penyakit_lainnya" class="block text-sm font-medium text-gray-700">Penyakit Lainnya</label>
                            <textarea name="penyakit_lainnya" id="penyakit_lainnya" class="form-textarea mt-1 block w-full"></textarea>
                        </div>

                        <!-- Riwayat Kesehatan - Penyakit bawaan/keturunan -->
                        <h3 class="text-lg font-medium text-gray-900 mb-4 mt-6">Riwayat Kesehatan - Penyakit bawaan/keturunan</h3>
                        <div class="mb-4">
                            <label class="inline-flex items-center">
                                <input type="checkbox" name="jantung" value="1" class="form-checkbox">
                                <span class="ml-2 text-sm text-gray-600">Jantung</span>
                            </label>
                        </div>
                        <div class="mb-4">
                            <label class="inline-flex items-center">
                                <input type="checkbox" name="diabetes" value="1" class="form-checkbox">
                                <span class="ml-2 text-sm text-gray-600">Diabetes</span>
                            </label>
                        </div>
                        <div class="mb-4">
                            <label class="inline-flex items-center">
                                <input type="checkbox" name="talasemia" value="1" class="form-checkbox">
                                <span class="ml-2 text-sm text-gray-600">Talasemia</span>
                            </label>
                        </div>
                        <div class="mb-4">
                            <label for="penyakit_bawaan_lainnya" class="block text-sm font-medium text-gray-700">Penyakit Bawaan Lainnya</label>
                            <textarea name="penyakit_bawaan_lainnya" id="penyakit_bawaan_lainnya" class="form-textarea mt-1 block w-full"></textarea>
                        </div>

                        <!-- Riwayat Kesehatan - Alergi -->
                        <h3 class="text-lg font-medium text-gray-900 mb-4 mt-6">Riwayat Kesehatan - Alergi</h3>
                        <div class="mb-4">
                            <label class="inline-flex items-center">
                                <input type="checkbox" name="alergi_obat" value="1" class="form-checkbox">
                                <span class="ml-2 text-sm text-gray-600">Alergi Obat-obatan</span>
                            </label>
                        </div>
                        <div class="mb-4">
                            <label class="inline-flex items-center">
                                <input type="checkbox" name="alergi_makanan" value="1" class="form-checkbox">
                                <span class="ml-2 text-sm text-gray-600">Alergi Makanan</span>
                            </label>
                        </div>

                        <!-- Siklus Menstruasi (hanya untuk perempuan) -->
                        <h3 class="text-lg font-medium text-gray-900 mb-4 mt-6">Siklus Menstruasi (hanya untuk perempuan)</h3>
                        <div class="mb-4">
                            <label for="siklus_menstruasi" class="block text-sm font-medium text-gray-700">Siklus Menstruasi</label>
                            <textarea name="siklus_menstruasi" id="siklus_menstruasi" class="form-textarea mt-1 block w-full"></textarea>
                        </div>

                        <div class="flex items-center justify-end mt-4">
                            <button type="submit" class="btn btn-primary">
                                Simpan Santri
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>