<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Dashboard Kader') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Grafik Pertumbuhan Santri</h3>
                    @foreach ($growthData as $data)
                        <div class="mb-8">
                            <h4 class="font-semibold text-gray-800">{{ $data['santri_name'] }}</h4>
                            <canvas id="growthChart{{ $loop->index }}"></canvas>
                        </div>
                    @endforeach

                    <h3 class="text-lg font-medium text-gray-900 mb-4 mt-8">Statistik Anemia</h3>
                    <canvas id="anemiaChart"></canvas>

                    <h3 class="text-lg font-medium text-gray-900 mb-4 mt-8">Statistik Gizi</h3>
                    <canvas id="nutritionChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Growth Charts
            @foreach ($growthData as $data)
                var ctxGrowth{{ $loop->index }} = document.getElementById('growthChart{{ $loop->index }}').getContext('2d');
                new Chart(ctxGrowth{{ $loop->index }}, {
                    type: 'line',
                    data: {
                        labels: @json($data['labels']),
                        datasets: [
                            {
                                label: 'Berat Badan (kg)',
                                data: @json($data['berat_badan']),
                                borderColor: 'rgba(75, 192, 192, 1)',
                                tension: 0.1
                            },
                            {
                                label: 'Tinggi Badan (cm)',
                                data: @json($data['tinggi_badan']),
                                borderColor: 'rgba(153, 102, 255, 1)',
                                tension: 0.1
                            },
                            {
                                label: 'IMT',
                                data: @json($data['imt']),
                                borderColor: 'rgba(255, 159, 64, 1)',
                                tension: 0.1
                            }
                        ]
                    },
                    options: {
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            @endforeach

            // Anemia Chart
            var ctxAnemia = document.getElementById('anemiaChart').getContext('2d');
            new Chart(ctxAnemia, {
                type: 'pie',
                data: {
                    labels: @json($anemiaData['labels']),
                    datasets: [{
                        data: @json($anemiaData['data']),
                        backgroundColor: [
                            'rgba(75, 192, 192, 0.6)',
                            'rgba(255, 99, 132, 0.6)'
                        ],
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: 'Statistik Anemia'
                        }
                    }
                }
            });

            // Nutrition Chart
            var ctxNutrition = document.getElementById('nutritionChart').getContext('2d');
            new Chart(ctxNutrition, {
                type: 'pie',
                data: {
                    labels: @json($nutritionData['labels']),
                    datasets: [{
                        data: @json($nutritionData['data']),
                        backgroundColor: [
                            'rgba(75, 192, 192, 0.6)',
                            'rgba(255, 206, 86, 0.6)',
                            'rgba(255, 99, 132, 0.6)'
                        ],
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: 'Statistik Gizi'
                        }
                    }
                }
            });
        });
    </script>
</x-app-layout>