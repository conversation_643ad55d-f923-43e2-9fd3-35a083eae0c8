<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Santri;

class AdminSantriController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Santri::query();

        // Pencarian berdasarkan nama atau NIS
        if ($request->has('search')) {
            $search = $request->input('search');
            $query->where('nama', 'like', '%' . $search . '%')
                  ->orWhere('nis', 'like', '%' . $search . '%');
        }

        // Filter berdasarkan kelas
        if ($request->has('kelas') && $request->input('kelas') != '') {
            $query->where('kelas', $request->input('kelas'));
        }

        // Filter berdasarkan jenis kelamin
        if ($request->has('jenis_kelamin') && $request->input('jenis_kelamin') != '') {
            $query->where('jenis_kelamin', $request->input('jenis_kelamin'));
        }

        $santris = $query->paginate(10); // Menggunakan paginasi
        
        return view('admin.santri.index', compact('santris'));
    }
}
