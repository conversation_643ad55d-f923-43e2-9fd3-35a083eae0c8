<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        // Create users with different roles
        User::factory()->create([
            'name' => 'Santri User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'user_type' => 'santri',
        ]);

        User::factory()->create([
            'name' => 'Kader User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'user_type' => 'kader',
        ]);

        User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'user_type' => 'admin',
        ]);
    }
}
