<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Manajemen Data Santri (Admin)') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Daftar Santri</h3>

                    <form action="{{ route('admin.santri.index') }}" method="GET" class="mb-4">
                        <div class="flex space-x-4">
                            <input type="text" name="search" placeholder="Cari Nama atau NIS" class="form-input w-1/3" value="{{ request('search') }}">
                            <select name="kelas" class="form-select w-1/4">
                                <option value="">Se<PERSON><PERSON></option>
                                <option value="10" {{ request('kelas') == '10' ? 'selected' : '' }}>10</option>
                                <option value="11" {{ request('kelas') == '11' ? 'selected' : '' }}>11</option>
                                <option value="12" {{ request('kelas') == '12' ? 'selected' : '' }}>12</option>
                            </select>
                            <select name="jenis_kelamin" class="form-select w-1/4">
                                <option value="">Semua Jenis Kelamin</option>
                                <option value="Laki-laki" {{ request('jenis_kelamin') == 'Laki-laki' ? 'selected' : '' }}>Laki-laki</option>
                                <option value="Perempuan" {{ request('jenis_kelamin') == 'Perempuan' ? 'selected' : '' }}>Perempuan</option>
                            </select>
                            <button type="submit" class="btn btn-primary">Filter</button>
                        </div>
                    </form>

                    <table class="table">
                        <thead>
                            <tr>
                                <th>Nama</th>
                                <th>Umur</th>
                                <th>Jenis Kelamin</th>
                                <th>Kelas</th>
                                <th>NIS</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($santris as $santri)
                                <tr>
                                    <td>{{ $santri->nama }}</td>
                                    <td>{{ $santri->umur }}</td>
                                    <td>{{ $santri->jenis_kelamin }}</td>
                                    <td>{{ $santri->kelas }}</td>
                                    <td>{{ $santri->nis }}</td>
                                    <td>
                                        <a href="#" class="btn btn-sm btn-primary">Detail</a>
                                        <a href="#" class="btn btn-sm btn-warning">Edit</a>
                                        <form action="#" method="POST" class="inline-block">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-danger">Hapus</button>
                                        </form>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>

                    <div class="mt-4">
                        {{ $santris->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>