<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Dashboard Santri') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Data Diri Santri</h3>
                    <p><strong>Nama:</strong> {{ $santri->nama }}</p>
                    <p><strong>Umur:</strong> {{ $santri->umur }}</p>
                    <p><strong>Jenis <PERSON>:</strong> {{ $santri->jenis_kelamin }}</p>
                    <p><strong>Kelas:</strong> {{ $santri->kelas }}</p>
                    <p><strong>NIS:</strong> {{ $santri->nis }}</p>

                    <h3 class="text-lg font-medium text-gray-900 mb-4 mt-6">Riwayat Pemeriksaan</h3>
                    @if ($santri->pemeriksaans->count() > 0)
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Tanggal</th>
                                    <th>Berat Badan</th>
                                    <th>Tinggi Badan</th>
                                    <th>IMT</th>
                                    <th>Tekanan Darah</th>
                                    <th>Hb</th>
                                    <th>Masalah/Konseling</th>
                                    <th>Tablet Tambah Darah</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($santri->pemeriksaans as $pemeriksaan)
                                    <tr>
                                        <td>{{ $pemeriksaan->created_at->format('d M Y') }}</td>
                                        <td>{{ $pemeriksaan->berat_badan }} kg</td>
                                        <td>{{ $pemeriksaan->tinggi_badan }} cm</td>
                                        <td>{{ $pemeriksaan->imt }}</td>
                                        <td>{{ $pemeriksaan->tekanan_darah }}</td>
                                        <td>{{ $pemeriksaan->hemoglobin }} g/dL</td>
                                        <td>{{ $pemeriksaan->masalah }}</td>
                                        <td>{{ $pemeriksaan->tablet_tambah_darah ? 'Ya' : 'Tidak' }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    @else
                        <p>Belum ada riwayat pemeriksaan.</p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>