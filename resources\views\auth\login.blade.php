<x-guest-layout>
    <!-- Session Status -->
    <x-auth-session-status class="mb-4" :status="session('status')" />

    <form method="POST" action="{{ route('login') }}">
        @csrf

        <!-- Email Address -->
        <div>
            <x-input-label for="email" :value="__('Email')" />
            <x-text-input id="email" class="block mt-1 w-full" type="email" name="email" :value="old('email')" required autofocus autocomplete="username" />
            <x-input-error :messages="$errors->get('email')" class="mt-2" />
        </div>

        <!-- Password -->
        <div class="mt-4">
            <x-input-label for="password" :value="__('Password')" />

            <x-text-input id="password" class="block mt-1 w-full"
                            type="password"
                            name="password"
                            required autocomplete="current-password" />

            <x-input-error :messages="$errors->get('password')" class="mt-2" />
        </div>

        <!-- Remember Me -->
        <div class="block mt-4">
            <label for="remember_me" class="inline-flex items-center">
                <input id="remember_me" type="checkbox" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500" name="remember">
                <span class="ms-2 text-sm text-gray-600">{{ __('Remember me') }}</span>
            </label>
        </div>

        <div class="flex items-center justify-end mt-4">
            @if (Route::has('password.request'))
                <a class="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" href="{{ route('password.request') }}">
                    {{ __('Forgot your password?') }}
                </a>
            @endif

            <x-primary-button class="ms-3">
                {{ __('Log in') }}
            </x-primary-button>
        </div>

        <!-- Tombol untuk testing -->
        <div class="mt-4">
            <p class="text-sm text-gray-600">Untuk testing:</p>
            <div class="flex space-x-2 mt-2">
                <button type="button" class="text-xs bg-blue-600 hover:bg-blue-800 text-white py-2 px-3 rounded font-semibold" onclick="fillCredentials('<EMAIL>', 'password123')">
                    Login sebagai Santri
                </button>
                <button type="button" class="text-xs bg-green-600 hover:bg-green-800 text-white py-2 px-3 rounded font-semibold" onclick="fillCredentials('<EMAIL>', 'password123')">
                    Login sebagai Kader
                </button>
                <button type="button" class="text-xs bg-purple-600 hover:bg-purple-800 text-white py-2 px-3 rounded font-semibold" onclick="fillCredentials('<EMAIL>', 'password123')">
                    Login sebagai Admin
                </button>
            </div>
        </div>
    </form>

    <script>
        function fillCredentials(email, password) {
            document.getElementById('email').value = email;
            document.getElementById('password').value = password;
        }
    </script>
</x-guest-layout>
