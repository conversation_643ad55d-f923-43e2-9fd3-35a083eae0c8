@extends('components.admin-layout')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header"><PERSON><PERSON><PERSON></div>

                <div class="card-body">
                    @if (session('status'))
                        <div class="alert alert-success" role="alert">
                            {{ session('status') }}
                        </div>
                    @endif

                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">Ana<PERSON><PERSON>ren <PERSON></div>
                                <div class="card-body">
                                    <canvas id="healthTrendChart" height="100"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    // Data dari controller
    const healthTrends = @json($healthTrends);

    // Grafik Tren Kesehatan
    const healthTrendCtx = document.getElementById('healthTrendChart').getContext('2d');
    const healthTrendChart = new Chart(healthTrendCtx, {
        type: 'line',
        data: {
            labels: healthTrends.labels,
            datasets: [{
                label: 'Indeks Kesehatan',
                data: healthTrends.data,
                borderColor: 'rgb(75, 192, 192)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
</script>
@endsection