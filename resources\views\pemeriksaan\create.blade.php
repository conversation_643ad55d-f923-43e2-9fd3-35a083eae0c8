@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">Input Pemeriksaan untuk {{ $santri->nama }}</div>

                <div class="card-body">
                    <form method="POST" action="{{ route('pemeriksaan.store', $santri->id) }}">
                        @csrf

                        <!-- Antropometri -->
                        <div class="form-group row mb-3">
                            <label class="col-md-4 col-form-label text-md-right font-weight-bold">Antropometri</label>
                        </div>

                        <!-- Berat <PERSON> -->
                        <div class="form-group row mb-3">
                            <label for="berat_badan" class="col-md-4 col-form-label text-md-right">Berat <PERSON><PERSON> (kg)</label>

                            <div class="col-md-6">
                                <input id="berat_badan" type="number" step="0.01" class="form-control @error('berat_badan') is-invalid @enderror" name="berat_badan" value="{{ old('berat_badan') }}" autocomplete="berat_badan">

                                @error('berat_badan')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <!-- Tinggi Badan -->
                        <div class="form-group row mb-3">
                            <label for="tinggi_badan" class="col-md-4 col-form-label text-md-right">Tinggi Badan (cm)</label>

                            <div class="col-md-6">
                                <input id="tinggi_badan" type="number" step="0.01" class="form-control @error('tinggi_badan') is-invalid @enderror" name="tinggi_badan" value="{{ old('tinggi_badan') }}" autocomplete="tinggi_badan">

                                @error('tinggi_badan')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <!-- LILA -->
                        <div class="form-group row mb-3">
                            <label for="lila" class="col-md-4 col-form-label text-md-right">LILA (cm)</label>

                            <div class="col-md-6">
                                <input id="lila" type="number" step="0.01" class="form-control @error('lila') is-invalid @enderror" name="lila" value="{{ old('lila') }}" autocomplete="lila">

                                @error('lila')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <!-- Lingkar Perut -->
                        <div class="form-group row mb-3">
                            <label for="lingkar_perut" class="col-md-4 col-form-label text-md-right">Lingkar Perut (cm)</label>

                            <div class="col-md-6">
                                <input id="lingkar_perut" type="number" step="0.01" class="form-control @error('lingkar_perut') is-invalid @enderror" name="lingkar_perut" value="{{ old('lingkar_perut') }}" autocomplete="lingkar_perut">

                                @error('lingkar_perut')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <!-- IMT -->
                        <div class="form-group row mb-3">
                            <label for="imt" class="col-md-4 col-form-label text-md-right">IMT (kg/m²)</label>

                            <div class="col-md-6">
                                <input id="imt" type="number" step="0.01" class="form-control @error('imt') is-invalid @enderror" name="imt" value="{{ old('imt') }}" autocomplete="imt">

                                @error('imt')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <!-- Tekanan Darah -->
                        <div class="form-group row mb-3">
                            <label for="tekanan_darah" class="col-md-4 col-form-label text-md-right">Tekanan Darah</label>

                            <div class="col-md-6">
                                <input id="tekanan_darah" type="text" class="form-control @error('tekanan_darah') is-invalid @enderror" name="tekanan_darah" value="{{ old('tekanan_darah') }}" autocomplete="tekanan_darah">

                                @error('tekanan_darah')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <!-- Hemoglobin -->
                        <div class="form-group row mb-3">
                            <label for="hemoglobin" class="col-md-4 col-form-label text-md-right">Hemoglobin (g/dL)</label>

                            <div class="col-md-6">
                                <input id="hemoglobin" type="number" step="0.01" class="form-control @error('hemoglobin') is-invalid @enderror" name="hemoglobin" value="{{ old('hemoglobin') }}" autocomplete="hemoglobin">

                                @error('hemoglobin')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <!-- Masalah / Konseling -->
                        <div class="form-group row mb-3">
                            <label for="masalah" class="col-md-4 col-form-label text-md-right">Masalah / Konseling</label>

                            <div class="col-md-6">
                                <textarea id="masalah" class="form-control @error('masalah') is-invalid @enderror" name="masalah" rows="3" autocomplete="masalah">{{ old('masalah') }}</textarea>

                                @error('masalah')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <!-- Tablet Tambah Darah -->
                        <div class="form-group row mb-3">
                            <label for="tablet_tambah_darah" class="col-md-4 col-form-label text-md-right">Tablet Tambah Darah</label>

                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="tablet_tambah_darah" id="tablet_tambah_darah" {{ old('tablet_tambah_darah') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="tablet_tambah_darah">Diberikan</label>
                                </div>

                                @error('tablet_tambah_darah')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="form-group row mb-0">
                            <div class="col-md-6 offset-md-4">
                                <button type="submit" class="btn btn-primary">
                                    Simpan
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection