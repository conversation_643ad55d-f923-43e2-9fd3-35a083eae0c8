<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('santris', function (Blueprint $table) {
            $table->id();
            
            // Identitas santri
            $table->string('nama');
            $table->integer('umur');
            $table->string('jenis_kelamin');
            $table->string('kelas');
            $table->string('nis')->unique();
            
            // Riwayat kesehatan - Penyakit yang pernah diderita
            $table->boolean('epilepsi')->default(false);
            $table->boolean('asma')->default(false);
            $table->boolean('hepatitis')->default(false);
            $table->boolean('tuberculosis')->default(false);
            $table->boolean('patah_tulang')->default(false);
            $table->boolean('hernia')->default(false);
            $table->text('penyakit_lainnya')->nullable();
            
            // Riwayat kesehatan - Penyakit bawaan/keturunan
            $table->boolean('jantung')->default(false);
            $table->boolean('diabetes')->default(false);
            $table->boolean('talasemia')->default(false);
            $table->text('penyakit_bawaan_lainnya')->nullable();
            
            // Riwayat kesehatan - Alergi
            $table->boolean('alergi_obat')->default(false);
            $table->boolean('alergi_makanan')->default(false);
            
            // Riwayat kesehatan - Siklus menstruasi (hanya untuk perempuan)
            $table->text('siklus_menstruasi')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('santris');
    }
};
