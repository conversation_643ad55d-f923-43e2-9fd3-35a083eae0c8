@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Daftar Santri</span>
                    <a href="{{ route('santri.create') }}" class="btn btn-primary">Tambah Santri</a>
                </div>

                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success" role="alert">
                            {{ session('success') }}
                        </div>
                    @endif

                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Nama</th>
                                <th>Umur</th>
                                <th><PERSON><PERSON></th>
                                <th>Kelas</th>
                                <th>NIS</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($santris as $santri)
                                <tr>
                                    <td>{{ $santri->nama }}</td>
                                    <td>{{ $santri->umur }}</td>
                                    <td>{{ $santri->jenis_kelamin }}</td>
                                    <td>{{ $santri->kelas }}</td>
                                    <td>{{ $santri->nis }}</td>
                                    <td>
                                        <a href="{{ route('pemeriksaan.create', $santri->id) }}" class="btn btn-sm btn-primary">Input Pemeriksaan</a>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection