<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Santri extends Model
{
    protected $fillable = [
        'nama',
        'umur',
        'jenis_kelamin',
        'kelas',
        'nis',
        'epilepsi',
        'asma',
        'hepatitis',
        'tuberculosis',
        'patah_tulang',
        'hernia',
        'penyakit_lainnya',
        'jantung',
        'diabetes',
        'talasemia',
        'penyakit_bawaan_lainnya',
        'alergi_obat',
        'alergi_makanan',
        'siklus_menstruasi',
    ];

    public function pemeriksaans()
    {
        return $this->hasMany(Pemeriksaan::class);
    }
}
