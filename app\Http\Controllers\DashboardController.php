<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Pemeriksaan;
use App\Models\Santri;

class DashboardController extends Controller
{
    public function index()
    {
        // Mengambil data untuk grafik pertumbuhan
        $growthData = $this->getGrowthData();
        
        // Mengambil data untuk statistik anemia dan gizi
        $anemiaData = $this->getAnemiaData();
        $nutritionData = $this->getNutritionData();
        
        return view('kader.dashboard', compact('growthData', 'anemiaData', 'nutritionData'));
    }
    
    private function getGrowthData()
    {
        // Implementasi untuk mengambil data pertumbuhan dari database
        // Contoh: Ambil data pemeriksaan terbaru untuk beberapa santri
        $santris = Santri::limit(5)->get(); // Ambil 5 santri contoh
        $data = [];

        foreach ($santris as $santri) {
            $pemeriksaans = $santri->pemeriksaans()->orderBy('created_at', 'asc')->get();
            $labels = $pemeriksaans->map(fn($p) => $p->created_at->format('M Y'))->toArray();
            $beratBadan = $pemeriksaans->map(fn($p) => $p->berat_badan)->toArray();
            $tinggiBadan = $pemeriksaans->map(fn($p) => $p->tinggi_badan)->toArray();
            $imt = $pemeriksaans->map(fn($p) => $p->imt)->toArray();

            $data[] = [
                'santri_name' => $santri->nama,
                'labels' => $labels,
                'berat_badan' => $beratBadan,
                'tinggi_badan' => $tinggiBadan,
                'imt' => $imt,
            ];
        }

        return $data;
    }
    
    private function getAnemiaData()
    {
        // Implementasi untuk mengambil data anemia dari database
        // Contoh: Hitung jumlah santri dengan Hb di bawah normal
        $totalSantri = Santri::count();
        $anemiaCount = Pemeriksaan::where('hemoglobin', '<=', 12)->count(); // Contoh threshold anemia
        $normalCount = $totalSantri - $anemiaCount;

        return [
            'labels' => ['Normal', 'Anemia'],
            'data' => [$normalCount, $anemiaCount]
        ];
    }
    
    private function getNutritionData()
    {
        // Implementasi untuk mengambil data gizi dari database
        // Contoh: Hitung jumlah santri berdasarkan IMT
        $totalSantri = Santri::count();
        $giziKurangCount = Pemeriksaan::where('imt', '<=', 18.5)->count(); // Contoh IMT gizi kurang
        $giziNormalCount = Pemeriksaan::where('imt', '>', 18.5)->where('imt', '<=', 24.9)->count(); // Contoh IMT gizi normal
        $giziLebihCount = $totalSantri - $giziKurangCount - $giziNormalCount;

        return [
            'labels' => ['Gizi Normal', 'Gizi Kurang', 'Gizi Lebih'],
            'data' => [$giziNormalCount, $giziKurangCount, $giziLebihCount]
        ];
    }
}