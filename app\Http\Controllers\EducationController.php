<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class EducationController extends Controller
{
    public function index()
    {
        // Mengambil data untuk edukasi kesehatan
        $educationData = $this->getEducationData();
        
        return view('education.index', compact('educationData'));
    }
    
    private function getEducationData()
    {
        // Implementasi untuk mengambil data edukasi kesehatan
        // Ini adalah contoh sederhana, Anda mungkin perlu menyesuaikan dengan kebutuhan aplikasi
        return [
            [
                'title' => 'Pola Makan Sehat untuk Remaja',
                'content' => '<PERSON>kanan sehat untuk remaja harus mengandung protein, karbohidrat kompleks, lemak sehat, vitamin, dan mineral...'
            ],
            [
                'title' => 'Pentingnya Olahraga bagi Remaja',
                'content' => 'Olahraga rutin dapat membantu remaja menjaga berat badan ideal, meningkatkan mood, dan memperkuat tulang...'
            ],
            [
                'title' => '<PERSON><PERSON><PERSON>',
                'content' => '<PERSON><PERSON>ihan diri yang baik penting untuk mencegah infeksi dan menjaga kesehatan secara keseluruhan...'
            ]
        ];
    }
}
