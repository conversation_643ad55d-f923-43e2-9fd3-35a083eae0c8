<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pemeriksaans', function (Blueprint $table) {
            $table->id();
            
            // Foreign key ke tabel santris
            $table->unsignedBigInteger('santri_id');
            $table->foreign('santri_id')->references('id')->on('santris')->onDelete('cascade');
            
            // Antropometri
            $table->decimal('berat_badan', 5, 2)->nullable();
            $table->decimal('tinggi_badan', 5, 2)->nullable();
            $table->decimal('lila', 5, 2)->nullable();
            $table->decimal('lingkar_perut', 5, 2)->nullable();
            $table->decimal('imt', 5, 2)->nullable();
            
            // Tekanan darah
            $table->string('tekanan_darah')->nullable();
            
            // Hemoglobin
            $table->decimal('hemoglobin', 5, 2)->nullable();
            
            // Masalah / konseling
            $table->text('masalah')->nullable();
            
            // Tablet tambah darah
            $table->boolean('tablet_tambah_darah')->default(false);
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pemeriksaans');
    }
};
