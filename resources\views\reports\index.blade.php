@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">Lapor<PERSON> B<PERSON>nan - {{ $monthlyReportData['bulan'] }}</div>

                <div class="card-body">
                    @if (session('status'))
                        <div class="alert alert-success" role="alert">
                            {{ session('status') }}
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">Ringkasan Data</div>
                                <div class="card-body">
                                    <table class="table">
                                        <tr>
                                            <td>Ju<PERSON><PERSON> Santri</td>
                                            <td>{{ $monthlyReportData['jumlah_santri'] }}</td>
                                        </tr>
                                        <tr>
                                            <td><PERSON><PERSON><PERSON>emeriksaan</td>
                                            <td>{{ $monthlyReportData['jumlah_pemeriksaan'] }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">Kondisi Kesehatan</div>
                                <div class="card-body">
                                    <table class="table">
                                        <tr>
                                            <td>Anemia Ringan</td>
                                            <td>{{ $monthlyReportData['anemia_ringan'] }}</td>
                                        </tr>
                                        <tr>
                                            <td>Anemia Sedang</td>
                                            <td>{{ $monthlyReportData['anemia_sedang'] }}</td>
                                        </tr>
                                        <tr>
                                            <td>Anemia Berat</td>
                                            <td>{{ $monthlyReportData['anemia_berat'] }}</td>
                                        </tr>
                                        <tr>
                                            <td>Gizi Kurang</td>
                                            <td>{{ $monthlyReportData['gizi_kurang'] }}</td>
                                        </tr>
                                        <tr>
                                            <td>Gizi Buruk</td>
                                            <td>{{ $monthlyReportData['gizi_buruk'] }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <button class="btn btn-primary" onclick="window.print()">Cetak Laporan</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection