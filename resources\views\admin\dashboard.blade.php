<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Dashboard Admin') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Ringkasan Data</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                        <div class="bg-blue-100 p-4 rounded-lg">
                            <p class="text-sm text-gray-600">Total Santri</p>
                            <p class="text-2xl font-bold">{{ $totalSantri }}</p>
                        </div>
                        <div class="bg-green-100 p-4 rounded-lg">
                            <p class="text-sm text-gray-600">Total Kader</p>
                            <p class="text-2xl font-bold">{{ $totalKader }}</p>
                        </div>
                        <div class="bg-yellow-100 p-4 rounded-lg">
                            <p class="text-sm text-gray-600">Total Pemeriksaan</p>
                            <p class="text-2xl font-bold">{{ $totalPemeriksaan }}</p>
                        </div>
                    </div>

                    <h3 class="text-lg font-medium text-gray-900 mb-4 mt-8">Tren Pemeriksaan Bulanan</h3>
                    <canvas id="pemeriksaanTrendChart"></canvas>

                    <h3 class="text-lg font-medium text-gray-900 mb-4 mt-8">Statistik Kesehatan</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-800">Persentase Anemia</h4>
                            <p class="text-xl">{{ $anemiaPercentage }}%</p>
                        </div>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-800">Persentase Gizi</h4>
                            <p class="text-xl">Kurang: {{ $giziKurangPercentage }}%</p>
                            <p class="text-xl">Normal: {{ $giziNormalPercentage }}%</p>
                            <p class="text-xl">Lebih: {{ $giziLebihPercentage }}%</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Pemeriksaan Trend Chart
            var ctxPemeriksaanTrend = document.getElementById('pemeriksaanTrendChart').getContext('2d');
            new Chart(ctxPemeriksaanTrend, {
                type: 'line',
                data: {
                    labels: @json($months),
                    datasets: [
                        {
                            label: 'Jumlah Pemeriksaan',
                            data: @json($pemeriksaanCounts),
                            borderColor: 'rgba(54, 162, 235, 1)',
                            tension: 0.1,
                            fill: false
                        }
                    ]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        });
    </script>
</x-app-layout>