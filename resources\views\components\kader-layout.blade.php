<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ $title ?? 'SANTRI SEHAT - Kader' }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans antialiased">
    <div class="min-h-screen bg-gray-100">
        <!-- Page Heading -->
        @if (isset($header))
            <header class="bg-white shadow">
                <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                    {{ $header }}
                </div>
            </header>
        @endif

        <!-- Page Content -->
        <main class="flex">
            <!-- Sidebar -->
            <aside class="w-64 bg-white shadow h-screen sticky top-0">
                <div class="p-4">
                    <h2 class="text-xl font-semibold">KADER POSYANDU</h2>
                </div>
                <nav class="mt-5">
                    <a href="{{ route('kader.dashboard') }}" class="block py-2 px-4 text-sm hover:bg-gray-200 {{ request()->routeIs('kader.dashboard') ? 'bg-gray-200' : '' }}">Dashboard</a>
                    <a href="{{ route('kader.profile.edit') }}" class="block py-2 px-4 text-sm hover:bg-gray-200 {{ request()->routeIs('kader.profile.edit') ? 'bg-gray-200' : '' }}">Profil</a>
                    <a href="{{ route('santri.index') }}" class="block py-2 px-4 text-sm hover:bg-gray-200 {{ request()->routeIs('santri.index') ? 'bg-gray-200' : '' }}">Data Santri</a>
                    <a href="{{ route('kader.reports.index') }}" class="block py-2 px-4 text-sm hover:bg-gray-200 {{ request()->routeIs('kader.reports.index') ? 'bg-gray-200' : '' }}">Laporan Bulanan</a>
                </nav>
            </aside>

            <!-- Main Content -->
            <div class="flex-1 p-6">
                {{ $slot }}
            </div>
        </main>
    </div>
</body>
</html>