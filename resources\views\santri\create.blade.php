@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">Input Identitas Santri</div>

                <div class="card-body">
                    <form method="POST" action="{{ route('santri.store') }}">
                        @csrf

                        <!-- Nama -->
                        <div class="form-group row mb-3">
                            <label for="nama" class="col-md-4 col-form-label text-md-right">Nama</label>

                            <div class="col-md-6">
                                <input id="nama" type="text" class="form-control @error('nama') is-invalid @enderror" name="nama" value="{{ old('nama') }}" required autocomplete="nama" autofocus>

                                @error('nama')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <!-- Umur -->
                        <div class="form-group row mb-3">
                            <label for="umur" class="col-md-4 col-form-label text-md-right">Umur</label>

                            <div class="col-md-6">
                                <input id="umur" type="number" class="form-control @error('umur') is-invalid @enderror" name="umur" value="{{ old('umur') }}" required autocomplete="umur">

                                @error('umur')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <!-- Jenis Kelamin -->
                        <div class="form-group row mb-3">
                            <label for="jenis_kelamin" class="col-md-4 col-form-label text-md-right">Jenis Kelamin</label>

                            <div class="col-md-6">
                                <select id="jenis_kelamin" class="form-control @error('jenis_kelamin') is-invalid @enderror" name="jenis_kelamin" required>
                                    <option value="">Pilih Jenis Kelamin</option>
                                    <option value="Laki-laki" {{ old('jenis_kelamin') == 'Laki-laki' ? 'selected' : '' }}>Laki-laki</option>
                                    <option value="Perempuan" {{ old('jenis_kelamin') == 'Perempuan' ? 'selected' : '' }}>Perempuan</option>
                                </select>

                                @error('jenis_kelamin')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <!-- Kelas -->
                        <div class="form-group row mb-3">
                            <label for="kelas" class="col-md-4 col-form-label text-md-right">Kelas</label>

                            <div class="col-md-6">
                                <input id="kelas" type="text" class="form-control @error('kelas') is-invalid @enderror" name="kelas" value="{{ old('kelas') }}" required autocomplete="kelas">

                                @error('kelas')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <!-- NIS -->
                        <div class="form-group row mb-3">
                            <label for="nis" class="col-md-4 col-form-label text-md-right">NIS</label>

                            <div class="col-md-6">
                                <input id="nis" type="text" class="form-control @error('nis') is-invalid @enderror" name="nis" value="{{ old('nis') }}" required autocomplete="nis">

                                @error('nis')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <!-- Riwayat Kesehatan -->
                        <div class="form-group row mb-3">
                            <label class="col-md-4 col-form-label text-md-right">Riwayat Kesehatan</label>

                            <div class="col-md-6">
                                <!-- Penyakit yang pernah diderita -->
                                <div class="mb-3">
                                    <label class="font-weight-bold">Penyakit yang pernah diderita:</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="epilepsi" id="epilepsi" {{ old('epilepsi') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="epilepsi">Epilepsi</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="asma" id="asma" {{ old('asma') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="asma">Asma</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="hepatitis" id="hepatitis" {{ old('hepatitis') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="hepatitis">Hepatitis</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="tuberculosis" id="tuberculosis" {{ old('tuberculosis') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="tuberculosis">Tuberculosis</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="patah_tulang" id="patah_tulang" {{ old('patah_tulang') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="patah_tulang">Patah Tulang</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="hernia" id="hernia" {{ old('hernia') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="hernia">Hernia</label>
                                    </div>
                                    <div class="form-group mt-2">
                                        <label for="penyakit_lainnya">Lainnya:</label>
                                        <input type="text" class="form-control" name="penyakit_lainnya" id="penyakit_lainnya" value="{{ old('penyakit_lainnya') }}">
                                    </div>
                                </div>

                                <!-- Penyakit bawaan/keturunan -->
                                <div class="mb-3">
                                    <label class="font-weight-bold">Penyakit bawaan/keturunan:</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="jantung" id="jantung" {{ old('jantung') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="jantung">Jantung</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="diabetes" id="diabetes" {{ old('diabetes') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="diabetes">Diabetes</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="talasemia" id="talasemia" {{ old('talasemia') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="talasemia">Talasemia</label>
                                    </div>
                                    <div class="form-group mt-2">
                                        <label for="penyakit_bawaan_lainnya">Lainnya:</label>
                                        <input type="text" class="form-control" name="penyakit_bawaan_lainnya" id="penyakit_bawaan_lainnya" value="{{ old('penyakit_bawaan_lainnya') }}">
                                    </div>
                                </div>

                                <!-- Riwayat alergi -->
                                <div class="mb-3">
                                    <label class="font-weight-bold">Riwayat alergi:</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="alergi_obat" id="alergi_obat" {{ old('alergi_obat') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="alergi_obat">Obat-obatan</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="alergi_makanan" id="alergi_makanan" {{ old('alergi_makanan') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="alergi_makanan">Makanan</label>
                                    </div>
                                </div>

                                <!-- Siklus menstruasi (hanya untuk perempuan) -->
                                <div class="mb-3" id="siklus_menstruasi_field" style="display: none;">
                                    <label for="siklus_menstruasi" class="font-weight-bold">Siklus Menstruasi:</label>
                                    <textarea class="form-control" name="siklus_menstruasi" id="siklus_menstruasi">{{ old('siklus_menstruasi') }}</textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="form-group row mb-0">
                            <div class="col-md-6 offset-md-4">
                                <button type="submit" class="btn btn-primary">
                                    Simpan
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const jenisKelaminSelect = document.getElementById('jenis_kelamin');
        const siklusMenstruasiField = document.getElementById('siklus_menstruasi_field');

        function toggleSiklusMenstruasi() {
            if (jenisKelaminSelect.value === 'Perempuan') {
                siklusMenstruasiField.style.display = 'block';
            } else {
                siklusMenstruasiField.style.display = 'none';
            }
        }

        // Panggil fungsi saat halaman dimuat
        toggleSiklusMenstruasi();

        // Tambahkan event listener untuk perubahan jenis kelamin
        jenisKelaminSelect.addEventListener('change', toggleSiklusMenstruasi);
    });
</script>
@endsection